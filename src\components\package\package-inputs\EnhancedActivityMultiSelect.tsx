/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from "react";
import { input_field_css } from "../PackageForm";

export interface EnhancedActivityMultiSelectProps {
  initialData: any[];
  allData: any[];
  dataName: string;
  dataId: string;
  pHolder: string;
  setDatas: React.Dispatch<React.SetStateAction<any[]>>;
}

export default function EnhancedActivityMultiSelect(props: EnhancedActivityMultiSelectProps) {
  const [selectedData, setSelectedData] = useState<any[]>([]);
  const [restData, setRestData] = useState<any[]>(props.allData);
  const [view, setView] = useState(false);
  const [textInput, setTextInput] = useState("");
  const [selectedDestination, setSelectedDestination] = useState<string>("");
  const [destinations, setDestinations] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Group activities by category or location
  const groupedActivities = () => {
    const grouped: Record<string, any[]> = {};
    restData.forEach(activity => {
      const category = activity.category || activity.location || "Other";
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(activity);
    });
    return grouped;
  };

  function handleInput(inp: any) {
    props.setDatas((prev) => [...prev, inp]);
    setSelectedData((prev) => [...prev, inp]);
    setTextInput("");
  }

  function handleClose(inp: string) {
    const data = selectedData.filter((k) => {
      return k[props.dataId] !== inp;
    });
    props.setDatas(data);
    setSelectedData(data);
  }

  function handleInputText(inp: string) {
    setTextInput(inp);
    // Filtering is now handled in the useEffect
  }

  // Handle destination change
  function handleDestinationChange(destination: string) {
    setSelectedDestination(destination);
    // Filtering is now handled in the useEffect
  }

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setView(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Extract unique destinations from activities
  useEffect(() => {
    const uniqueDestinations = Array.from(
      new Set(
        props.allData
          .map((activity) => {
            // Try different possible destination field names
            return activity.location ||
                   activity.destination ||
                   activity.destinationName ||
                   activity.place ||
                   activity.city ||
                   activity.region;
          })
          .filter(Boolean)
      )
    ).sort();
    setDestinations(uniqueDestinations);
  }, [props.allData]);

  // Filter activities based on search text and selected destination
  useEffect(() => {
    let filteredData = props.allData.filter((k) => {
      return !selectedData.some((j) => j[props.dataId] === k[props.dataId]);
    });

    // Apply destination filter if selected
    if (selectedDestination) {
      filteredData = filteredData.filter(
        (activity) => {
          // Check all possible destination field names
          const activityDestination =
            activity.location ||
            activity.destination ||
            activity.destinationName ||
            activity.place ||
            activity.city ||
            activity.region;

          return activityDestination === selectedDestination;
        }
      );
    }

    // Apply text search filter
    if (textInput) {
      filteredData = filteredData.filter((activity) =>
        activity[props.dataName].toLowerCase().includes(textInput.toLowerCase())
      );
    }

    setRestData(filteredData);
  }, [selectedData, props.allData, selectedDestination, textInput, props.dataName, props.dataId]);

  useEffect(() => {
    setSelectedData(props.initialData);
  }, [props.initialData]);

  // Add all activities of a specific category
  function addAllOfCategory(category: string) {
    const activitiesToAdd = groupedActivities()[category] || [];
    if (activitiesToAdd.length > 0) {
      const newSelectedData = [...selectedData, ...activitiesToAdd];
      props.setDatas(newSelectedData);
      setSelectedData(newSelectedData);
    }
  }

  // Get a truncated description
  const getTruncatedDescription = (description: string, maxLength = 80) => {
    if (!description) return '';
    return description.length > maxLength
      ? description.substring(0, maxLength) + '...'
      : description;
  };

  return (
    <div className="w-full border relative py-2 text-xl px-2 flex flex-wrap bg-white rounded-md">
      {selectedData?.length > 0 ? (
        selectedData?.map((k) => (
          <div
            key={k[props.dataId]}
            className="text-sm text-white bg-blue-500 m-1 p-1 rounded-lg flex items-center"
          >
            <div className="flex flex-col">
              <div className="flex items-center">
                <span className="font-medium">{k[props.dataName]}</span>
                {k.price && <span className="ml-1 text-blue-100">₹{k.price}</span>}
              </div>
              {k.location && (
                <div className="text-xs text-blue-100">
                  {k.location}
                </div>
              )}
            </div>
            <div
              onClick={() => handleClose(k[props.dataId])}
              className="bg-blue-600 flex justify-center items-center cursor-pointer w-[20px] h-[20px] ml-4 rounded-full font-bold"
            >
              x
            </div>
          </div>
        ))
      ) : (
        ""
      )}
      <div className="relative" ref={dropdownRef}>
        <input
          ref={inputRef}
          placeholder={props.pHolder}
          onFocus={() => setView(true)}
          type="text"
          value={textInput}
          className={input_field_css}
          onInput={(e: any) => handleInputText(e.target.value)}
        />
        {view && (
          <div className="fixed inset-0 z-50 flex items-start justify-center pt-20 bg-black bg-opacity-30" onClick={() => setView(false)}>
            <div
              className="w-full max-w-2xl bg-white border border-gray-200 rounded-md shadow-xl max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center p-3 border-b border-gray-200 bg-gray-50">
                <h3 className="text-lg font-medium text-gray-900">Select Activities</h3>
                <button
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => setView(false)}
                >
                  ×
                </button>
              </div>

              {/* Search and filter controls */}
              <div className="p-3 border-b border-gray-200">
                {/* Search input */}
                <div className="flex items-center border border-gray-300 rounded-md bg-white overflow-hidden focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 mb-2">
                  <input
                    type="text"
                    placeholder="Search activities by name..."
                    className="w-full py-2 px-3 border-none focus:outline-none text-sm"
                    value={textInput}
                    onChange={(e) => handleInputText(e.target.value)}
                    autoFocus
                  />
                  {textInput && (
                    <button
                      type="button"
                      className="p-2 text-gray-400 hover:text-gray-600"
                      onClick={() => setTextInput("")}
                    >
                      ×
                    </button>
                  )}
                </div>

                {/* Destination filter */}
                <div className="flex items-center">
                  <label className="text-sm text-gray-600 mr-2">Filter by destination:</label>
                  <select
                    className="flex-1 py-1 px-2 border border-gray-300 rounded-md text-sm"
                    value={selectedDestination}
                    onChange={(e) => handleDestinationChange(e.target.value)}
                  >
                    <option value="">All Destinations</option>
                    {destinations.map((destination) => (
                      <option key={destination} value={destination}>
                        {destination}
                      </option>
                    ))}
                  </select>

                  {selectedDestination && (
                    <button
                      type="button"
                      className="ml-2 text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded"
                      onClick={() => setSelectedDestination("")}
                    >
                      Clear
                    </button>
                  )}
                </div>

                {/* Filter status */}
                {(textInput || selectedDestination) && (
                  <div className="mt-2 text-xs text-blue-600">
                    {restData.length} activities match your filters
                  </div>
                )}
              </div>

              {/* Activity List */}
              <div className="overflow-y-auto" style={{ maxHeight: 'calc(80vh - 120px)' }}>
                <div className="p-3">
                  {restData.length === 0 ? (
                    <div className="p-3 text-sm text-gray-500 text-center">
                      No activities found matching your search
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {Object.entries(groupedActivities()).map(([category, activities]) => (
                        <div key={category} className="mb-4">
                          <div className="flex justify-between items-center mb-2 bg-gray-50 p-2 rounded-md">
                            <h4 className="text-sm font-medium text-gray-700">{category} ({activities.length})</h4>
                            <button
                              className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded-md"
                              onClick={() => addAllOfCategory(category)}
                            >
                              Add All
                            </button>
                          </div>

                          <div className="space-y-2">
                            {activities.map((activity) => (
                              <div
                                key={activity[props.dataId]}
                                className="p-3 hover:bg-blue-50 rounded-md cursor-pointer border border-gray-100"
                                onClick={() => handleInput(activity)}
                              >
                                <div className="flex justify-between">
                                  <div>
                                    <div className="text-sm font-medium">{activity[props.dataName]}</div>
                                    {activity.location && (
                                      <div className="text-xs text-gray-500 mt-1">
                                        Location: {activity.location}
                                      </div>
                                    )}
                                    {activity.description && (
                                      <div className="text-xs text-gray-600 mt-1">
                                        {getTruncatedDescription(activity.description)}
                                      </div>
                                    )}
                                    {activity.duration && (
                                      <div className="text-xs text-gray-500 mt-1">
                                        Duration: {activity.duration}
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex flex-col items-end">
                                    {activity.price && (
                                      <div className="text-sm font-medium text-green-600">
                                        ₹{activity.price}
                                      </div>
                                    )}
                                    <button
                                      className="bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium py-1 px-2 rounded mt-2"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleInput(activity);
                                      }}
                                    >
                                      Add
                                    </button>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Footer with actions */}
              <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-end">
                <button
                  className="bg-gray-200 hover:bg-gray-300 text-gray-800 mr-2 py-2 px-4 rounded-md text-sm font-medium"
                  onClick={() => setView(false)}
                >
                  Cancel
                </button>
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium"
                  onClick={() => setView(false)}
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
