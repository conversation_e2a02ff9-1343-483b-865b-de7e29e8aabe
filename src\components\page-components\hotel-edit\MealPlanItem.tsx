import { Pencil, Maximize2, Minimize2, X } from "lucide-react";
import DeleteMp from "./DeleteMp";
import { useState } from "react";
import EditMp from "./EditMp";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export interface MealPlanGetData{
    mealPlan: string;
    roomPrice: number;
    adultPrice: number;
    childPrice: number;
    seasonType: string;
    startDate: string[];
    endDate: string[];
    gstPer: number;
    hotelId:string;
    hotelMealId:string;
    hotelRoomId:string;
}
interface MealPlanItemProps {
    roomId: string;
    mealPlanId: string;
    mealPlanType: string;
    startDate?: string[];
    endDate?: string[];
    mp: MealPlanGetData;
}

const MealPlanItem = ({ mealPlanType, roomId, mealPlanId, mp }: MealPlanItemProps) => {
    const [dialogOpen, setDialogOpen] = useState(false);
    const [isMaximized, setIsMaximized] = useState(false);

    // Helper to format date
    const formatDate = (dateStr?: string) => {
        if (!dateStr) return '-';
        const d = new Date(dateStr);
        return d.toLocaleDateString('en-IN', { year: 'numeric', month: 'short', day: 'numeric' });
    };

    // Handle dialog close and reset maximize state
    const handleDialogClose = (open: boolean) => {
        setDialogOpen(open);
        if (!open) {
            setIsMaximized(false);
        }
    };

    // Handle maximize/restore functionality
    const handleMaximizeToggle = () => {
        setIsMaximized(!isMaximized);
    };

    return (
        <>
            {mealPlanType ? (
                <div className="">
                    <div className="border w-full rounded p-3 bg-gray-50">
                        <div className="flex justify-between items-center mb-2">
                            <div className="flex items-center space-x-3">
                                <h1 className="text-slate-600 font-medium text-base">
                                    {mealPlanType.toUpperCase()}
                                </h1>
                            </div>
                            <div className="flex items-center gap-2">
                                <Dialog open={dialogOpen} onOpenChange={handleDialogClose}>
                                    <DialogTrigger asChild>
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            className="flex items-center gap-1.5 text-blue-600 border-blue-200 hover:bg-blue-50"
                                        >
                                            <Pencil size={14} /> Edit
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent 
                                        className={`${
                                            isMaximized 
                                                ? "w-screen h-screen max-w-none max-h-none m-0 rounded-none" 
                                                : "max-w-5xl max-h-[95vh]"
                                        } overflow-y-auto transition-all duration-300 ease-in-out`}
                                    >
                                        {/* Custom Header with Maximize/Minimize Button */}
                                        <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-white">
                                            <DialogTitle className="text-2xl font-bold text-blue-900 flex items-center gap-3">
                                                <div className="w-2 h-8 bg-blue-500 rounded-full"></div>
                                                Edit {mealPlanType.toUpperCase()} Meal Plan
                                            </DialogTitle>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={handleMaximizeToggle}
                                                    className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-700 transition-colors"
                                                    title={isMaximized ? "Restore window" : "Maximize window"}
                                                >
                                                    {isMaximized ? (
                                                        <Minimize2 className="h-4 w-4" />
                                                    ) : (
                                                        <Maximize2 className="h-4 w-4" />
                                                    )}
                                                </Button>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleDialogClose(false)}
                                                    className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-700 transition-colors"
                                                    title="Close window"
                                                >
                                                    <X className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                        
                                        {/* Content Area */}
                                        <div className={`${isMaximized ? "p-8" : "p-6"} bg-gray-50`}>
                                            <EditMp 
                                                mp={mp} 
                                                setEdit={handleDialogClose}
                                                onClose={() => {
                                                    handleDialogClose(false);
                                                    // Handle refresh if needed
                                                }}
                                                isMaximized={isMaximized}
                                            />
                                        </div>
                                    </DialogContent>
                                </Dialog>
                                <DeleteMp roomId={roomId} mealPlanId={mealPlanId} />
                            </div>
                        </div>
                        {/* Date Ranges Table */}
                        {mp.startDate && mp.endDate && mp.startDate.length > 0 ? (
                            <div className="overflow-x-auto">
                                <table className="min-w-full text-sm border rounded">
                                    <thead className="bg-blue-50">
                                        <tr>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Start Date</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">End Date</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Room Price</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Adult Price</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Child Price</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">Season</th>
                                            <th className="px-3 py-2 text-left font-semibold text-blue-900">GST %</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {mp.startDate.map((start, idx) => (
                                            <tr key={idx} className="border-b last:border-b-0">
                                                <td className="px-3 py-2">{formatDate(start)}</td>
                                                <td className="px-3 py-2">{formatDate(mp.endDate[idx])}</td>
                                                <td className="px-3 py-2">₹{mp.roomPrice}</td>
                                                <td className="px-3 py-2">{mp.adultPrice ? `₹${mp.adultPrice}` : '-'}</td>
                                                <td className="px-3 py-2">{mp.childPrice ? `₹${mp.childPrice}` : '-'}</td>
                                                <td className="px-3 py-2">{mp.seasonType || '-'}</td>
                                                <td className="px-3 py-2">{mp.gstPer ? `${mp.gstPer}%` : '-'}</td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-gray-500 text-sm italic">No date ranges set for this meal plan.</div>
                        )}
                    </div>
                </div>
            ) : (
                <>
                    No meal plans
                </>
            )}
        </>
    );
};

export default MealPlanItem;
