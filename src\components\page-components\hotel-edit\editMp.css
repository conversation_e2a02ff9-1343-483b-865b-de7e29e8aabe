/* Minimal CSS for the EditMp component calendar */

/* Custom scrollbar for better UX */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure the calendar wrapper takes full width */
.rdrDateRangePickerWrapper {
  width: 100%;
}

/* Ensure the calendar month takes full width */
.rdrMonth {
  width: 100% !important;
}

/* Style date display items with corresponding colors */
.rdrDateDisplayWrapper {
  margin-bottom: 10px;
}

.rdrDateDisplayItem {
  border-radius: 8px !important;
  border: 2px solid #e5e7eb !important;
  padding: 8px 12px !important;
  margin: 4px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.rdrDateDisplayItem:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.rdrDateDisplayItem input {
  font-weight: 600 !important;
  text-align: center !important;
  color: inherit !important;
  background: transparent !important;
  border: none !important;
}

/* Dynamic color classes for date display items */
.rdrDateDisplayItem[data-color] {
  border-color: var(--range-color) !important;
  background-color: var(--range-color-light) !important;
  color: var(--range-color) !important;
}

.rdrDateDisplayItem[data-color]:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--range-color);
  border-radius: 4px 0 0 4px;
}

/* Enhanced calendar layout for maximized mode */
.rdrMonthsHorizontal-maximized {
  width: 100% !important;
  min-height: 400px !important;
}

.rdrMonthsHorizontal-maximized .rdrMonths {
  display: flex !important;
  justify-content: space-between !important;
  gap: 15px !important;
  width: 100% !important;
}

.rdrMonthsHorizontal-maximized.flex-1 {
  flex: 1 !important;
}

.rdrMonthsHorizontal-maximized .rdrMonth {
  flex: 1 !important;
  max-width: calc(50% - 7.5px) !important;
  margin: 0 !important;
}

/* Better spacing for maximized calendar */
.rdrMonthsHorizontal-maximized .rdrWeekDays,
.rdrMonthsHorizontal-maximized .rdrDays {
  width: 100% !important;
}

.rdrMonthsHorizontal-maximized .rdrDay {
  height: 45px !important;
  width: 45px !important;
}

.rdrMonthsHorizontal-maximized .rdrDayNumber {
  font-size: 14px !important;
  line-height: 45px !important;
}

/* Responsive calendar container */
@media (max-width: 1200px) {
  .rdrMonthsHorizontal-maximized .rdrMonth {
    max-width: 100% !important;
  }
  
  .rdrMonthsHorizontal-maximized .rdrMonths {
    flex-direction: column !important;
    gap: 10px !important;
  }
}
