/**
 * Setup script for Tripmilestone Admin Backend
 * This script:
 * 1. Creates necessary directories
 * 2. Sets up environment variables
 * 3. Checks Python dependencies
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Directories to create
const directories = [
    'uploads',
  'logs'
];

// Create .env file if it doesn't exist
const envFile = path.join(__dirname, '.env');
const envExample = `# Linode Object Storage Configuration
LINODE_BUCKET_NAME=tripemilestone
LINODE_ENDPOINT=https://in-maa-1.linodeobjects.com
LINODE_ACCESS_KEY=your_access_key
LINODE_SECRET_KEY=your_secret_key

# Server Configuration
PORT=3001
NODE_ENV=development
`;

// Create directories
console.log('Creating necessary directories...');
directories.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dir}`);
  } else {
    console.log(`Directory already exists: ${dir}`);
  }
});

// Create .env file if it doesn't exist
if (!fs.existsSync(envFile)) {
  console.log('Creating .env file...');
  fs.writeFileSync(envFile, envExample);
  console.log('.env file created. Please update it with your Linode credentials.');
} else {
  console.log('.env file already exists.');
}

// Check Python installation
console.log('\nChecking Python installation...');
try {
  const pythonVersion = execSync('python --version').toString().trim();
  console.log(`Python is installed: ${pythonVersion}`);
} catch (error) {
  console.error('Python is not installed or not in PATH. Please install Python 3.x');
  process.exit(1);
}

// Check Python dependencies
console.log('\nChecking Python dependencies...');
try {
  // Copy requirements.txt to backend directory if it doesn't exist
  const requirementsFile = path.join(__dirname, 'requirements.txt');
  if (!fs.existsSync(requirementsFile)) {
    const sourceRequirements = path.join(__dirname, '..', 'requirements.txt');
    if (fs.existsSync(sourceRequirements)) {
      fs.copyFileSync(sourceRequirements, requirementsFile);
      console.log('Copied requirements.txt to backend directory');
    } else {
      console.log('requirements.txt not found. Creating a new one...');
      fs.writeFileSync(requirementsFile, 'pdfplumber==0.10.2\npandas==2.1.3\npytesseract==0.3.10\npdf2image==1.16.3\npillow==10.1.0\nspacy==3.7.2\n');
    }
  }

  // Ask user if they want to install Python dependencies
  rl.question('Do you want to install Python dependencies? (y/n): ', (answer) => {
    if (answer.toLowerCase() === 'y') {
      console.log('Installing Python dependencies...');
      try {
        execSync('pip install -r requirements.txt', { cwd: __dirname, stdio: 'inherit' });
        console.log('Python dependencies installed successfully.');
      } catch (error) {
        console.error('Error installing Python dependencies:', error.message);
      }
    } else {
      console.log('Skipping Python dependencies installation.');
    }
    
    console.log('\nSetup complete!');
    console.log('Next steps:');
    console.log('1. Update the .env file with your Linode credentials');
    console.log('2. Run "npm install" to install Node.js dependencies');
    console.log('3. Run "npm start" to start the server');
    
    rl.close();
  });
} catch (error) {
  console.error('Error checking Python dependencies:', error.message);
  rl.close();
}
