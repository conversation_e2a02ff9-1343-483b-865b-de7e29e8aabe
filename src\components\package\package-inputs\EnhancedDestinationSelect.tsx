/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from "react";
import { label_css } from "../PackageForm";

export interface EnhancedDestinationSelectProps {
  inputName: string;
  allData: any[];
  dataName: string;
  dataId: string;
  setData: React.Dispatch<React.SetStateAction<string>>;
  setDataName: React.Dispatch<React.SetStateAction<string>>;
  pHolder: string;
  dayCount: number;
  setDayCount: React.Dispatch<React.SetStateAction<number>>;
  addDestination: (name: string, id: string, days: number) => void;
}

export default function EnhancedDestinationSelect(props: EnhancedDestinationSelectProps) {
  const [view, setView] = useState(false);
  const [restData, setRestData] = useState<any[]>([]);
  const [regions, setRegions] = useState<string[]>([]);
  const [selectedRegion, setSelectedRegion] = useState<string>("");
  const [popularDestinations, setPopularDestinations] = useState<any[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Extract regions and popular destinations
  useEffect(() => {
    // Extract unique regions
    const uniqueRegions = Array.from(
      new Set(
        props.allData
          .map((destination) => destination.region || destination.state)
          .filter(Boolean)
      )
    ).sort();
    setRegions(uniqueRegions);

    // Set popular destinations (this is just an example - you might have a different way to determine popular destinations)
    // For now, we'll just take the first 5 destinations
    setPopularDestinations(props.allData.slice(0, 5));
  }, [props.allData]);

  function handleInput(inp: string) {
    props.setDataName(inp);
    setView(true);
    const data = props.allData.filter((k) =>
      k[props.dataName].toLowerCase()?.includes(inp.toLowerCase())
    );
    setRestData(data);
  }

  function handleClick(k: any) {
    props.setDataName(k[props.dataName]);
    props.setData(k[props.dataId]);
    setView(false);
  }

  function handleRegionChange(region: string) {
    setSelectedRegion(region);
    if (region) {
      const filteredData = props.allData.filter(
        (destination) =>
          (destination.region && destination.region === region) ||
          (destination.state && destination.state === region)
      );
      setRestData(filteredData);
    } else {
      setRestData(props.allData);
    }
  }

  function handleAddDestination(destination: any) {
    props.addDestination(
      destination[props.dataName],
      destination[props.dataId],
      props.dayCount
    );
    props.setDataName("");
    props.setData("");
    setView(false);
  }

  // Handle clicks outside the dropdown to close it
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setView(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setRestData(props.allData);
  }, [props.allData]);

  return (
    <div className="relative" ref={dropdownRef}>
      <label htmlFor={props.dataName} className={`${label_css} block mb-1`}>
        {props.pHolder} {props.pHolder === "Destination" && <span className="text-red-500">*</span>}
      </label>

      <div className="flex items-center">
        <div className="flex-1 relative">
          <input
            ref={inputRef}
            type="text"
            id={props.dataName}
            value={props.inputName}
            className="w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder={`Search ${props.pHolder}...`}
            onFocus={() => setView(true)}
            onInput={(e: any) => handleInput(e.target.value)}
          />

          {props.inputName && (
            <button
              type="button"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              onClick={() => {
                props.setDataName('');
                props.setData('');
              }}
            >
              ×
            </button>
          )}
        </div>

        <div className="ml-2">
          <label htmlFor="night-count" className={`${label_css} block mb-1`}>
            Nights
          </label>
          <select
            id="night-count"
            value={props.dayCount}
            onChange={(e) => props.setDayCount(parseInt(e.target.value))}
            className="w-20 py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
              <option key={num} value={num}>{num}</option>
            ))}
          </select>
        </div>
      </div>

      {view && (
        <div className="absolute z-50 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-[350px] overflow-y-auto">
          {/* Region filter */}
          <div className="p-3 border-b border-gray-200">
            <label className="text-sm text-gray-600 block mb-1">Filter by region:</label>
            <div className="flex">
              <select
                className="flex-1 py-1 px-2 border border-gray-300 rounded-md text-sm"
                value={selectedRegion}
                onChange={(e) => handleRegionChange(e.target.value)}
              >
                <option value="">All Regions</option>
                {regions.map((region) => (
                  <option key={region} value={region}>
                    {region}
                  </option>
                ))}
              </select>

              {selectedRegion && (
                <button
                  type="button"
                  className="ml-2 text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded flex items-center"
                  onClick={() => handleRegionChange("")}
                >
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Clear
                </button>
              )}
            </div>
          </div>

          {/* Popular destinations */}
          {!props.inputName && !selectedRegion && (
            <div className="p-3 border-b border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Popular Destinations</h4>
              <div className="grid grid-cols-2 gap-2">
                {popularDestinations.map((destination) => (
                  <div
                    key={destination[props.dataId]}
                    className="p-2 hover:bg-blue-50 rounded-md cursor-pointer border border-gray-100 flex justify-between items-center"
                    onClick={() => handleClick(destination)}
                  >
                    <span className="text-sm">{destination[props.dataName]}</span>
                    <button
                      className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 px-2 py-1 rounded flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddDestination(destination);
                      }}
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Destination list */}
          <div className="p-3">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              {props.inputName || selectedRegion ? "Matching Destinations" : "All Destinations"}
              {restData.length > 0 && ` (${restData.length})`}
            </h4>

            {restData.length === 0 ? (
              <div className="text-sm text-gray-500 text-center py-2">
                No destinations found
              </div>
            ) : (
              <div className="space-y-1">
                {restData.map((destination) => (
                  <div
                    key={destination[props.dataId]}
                    className="p-2 hover:bg-blue-50 rounded-md cursor-pointer border border-gray-100 flex justify-between items-center"
                    onClick={() => handleClick(destination)}
                  >
                    <div>
                      <div className="text-sm font-medium">{destination[props.dataName]}</div>
                      {destination.region && (
                        <div className="text-xs text-gray-500">
                          {destination.region}
                          {destination.state && destination.state !== destination.region && `, ${destination.state}`}
                        </div>
                      )}
                    </div>
                    <button
                      className="text-xs bg-blue-600 text-white hover:bg-blue-700 px-2 py-1 rounded flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddDestination(destination);
                      }}
                    >
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add ({props.dayCount} nights)
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
