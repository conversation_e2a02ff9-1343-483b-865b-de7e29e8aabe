import React, { useEffect, useState, useCallback } from "react"
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Custom TableCell with reduced padding
const TableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
>(({ className, ...props }, ref) => (
  <td
    ref={ref}
    className={`py-2.5 px-4 align-middle [&:has([role=checkbox])]:pr-0 ${className || ""}`}
    {...props}
  />
))
import { Input } from "@/components/ui/input"
import { ListFilter } from "lucide-react"
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { getPackages } from "@/utils/api-functions/getPackages"

// Debounce function to limit API calls during search
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  totalDocs: number
  totalPages: number
  setPackages: (data: any) => void
  searchTerm: string
  setSearchTerm: (term: string) => void
  pageNo: number
  setPageNo: (page: number) => void
}

export function DataTable<TData, TValue>({
  columns,
  data,
  totalDocs,
  totalPages,
  setPackages,
  searchTerm,
  setSearchTerm,
  pageNo,
  setPageNo
}: DataTableProps<TData, TValue>) {
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [pageSize, setPageSize] = useState(10)
  const [pageIndex, setPageIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  // Use debounced search term to prevent excessive API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 300)

  // Reset to first page when filters change
  React.useEffect(() => {
    setPageIndex(0)
    setPageNo(0)
  }, [debouncedSearchTerm, setPageNo])

  // Sync pageIndex with pageNo
  useEffect(() => {
    setPageIndex(pageNo)
  }, [pageNo])

  // Sync pageNo with pageIndex
  useEffect(() => {
    if (pageNo !== pageIndex) {
      setPageNo(pageIndex)
    }
  }, [pageIndex, pageNo, setPageNo])

  // Memoized fetch function to prevent unnecessary recreations
  const fetchPackages = useCallback(async () => {
    setIsLoading(true)
    try {
      const response = await getPackages(pageIndex * pageSize, debouncedSearchTerm, pageSize);
      setPackages(response);
    } catch (error) {
      console.error("Error fetching packages:", error);
    } finally {
      setIsLoading(false)
    }
  }, [pageSize, pageIndex, debouncedSearchTerm, setPackages]);

  // Fetch packages when relevant dependencies change
  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      columnFilters,
      pagination: { pageIndex, pageSize },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const next = updater({ pageIndex, pageSize })
        setPageIndex(next.pageIndex)
        setPageSize(next.pageSize)
      } else {
        setPageIndex(updater.pageIndex)
        setPageSize(updater.pageSize)
      }
    },
    manualPagination: true,
    pageCount: totalPages,
  })

  // Page size options
  const pageSizeOptions = [10, 25, 50, 100]

  return (
    <>
      <div className="flex flex-col space-y-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="font-bold text-xl flex items-center">
              <span className="bg-appprimary text-white p-2 rounded-full mr-2">
                {totalDocs}
              </span>
              <span className="font-medium text-gray-800">Packages</span>
              {searchTerm && (
                <span className="ml-2 text-sm font-normal text-gray-500">
                  (filtered)
                </span>
              )}
            </h1>
          </div>

          <a
            href="/packages/add"
            className="bg-appprimary hover:bg-green-600 text-white px-4 py-2.5 rounded-md transition-colors duration-200 flex items-center space-x-1 font-medium"
          >
            <span>+</span>
            <span>Create Package</span>
          </a>
        </div>

        <div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg border border-gray-200">
          <div className="relative w-96">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              {searchTerm !== debouncedSearchTerm ? (
                <div className="h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <Input
              placeholder="Search package name..."
              value={searchTerm}
              onChange={(event) => setSearchTerm(event.target.value)}
              className="pl-10 pr-10 py-2 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-md w-full"
              disabled={isLoading && searchTerm === debouncedSearchTerm}
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                disabled={isLoading}
              >
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="border-gray-300 text-gray-700 flex items-center space-x-1">
                  <ListFilter size={16} />
                  <span>Columns</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      <div className="rounded-md border border-gray-200 shadow-sm overflow-hidden relative">
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-2 text-sm text-gray-600">Loading packages...</p>
            </div>
          </div>
        )}
        <Table className="border-collapse">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="bg-gray-50">
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="py-3 text-sm font-semibold text-gray-900">
                    {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className={`
                    ${row.index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                    hover:bg-blue-50 transition-colors duration-150
                    border-b border-gray-100
                  `}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="text-sm">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  {isLoading ? "Loading..." : "No results found."}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex flex-col items-center gap-4 mt-6 mb-4">
        {/* Page info */}
        <div className="text-sm text-gray-600">
          Showing {pageIndex * pageSize + 1} to {Math.min((pageIndex + 1) * pageSize, totalDocs)} of {totalDocs} packages
        </div>

        {/* Pagination controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={() => table.setPageIndex(0)}
            disabled={!table.getCanPreviousPage()}
            className="p-2 rounded-md border border-gray-200 text-gray-600 bg-white hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed"
            title="First Page"
          >
            {'<<'}
          </button>
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="p-2 rounded-md border border-gray-200 text-gray-600 bg-white hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed"
            title="Previous Page"
          >
            {'<'}
          </button>

          {/* Only show up to 5 page buttons at a time with ellipsis for better UX */}
          <div className="flex items-center">
            {Array.from({ length: totalPages }).map((_, i) => {
              // Show first page, last page, current page, and pages around current page
              const showPageButton =
                i === 0 || // First page
                i === totalPages - 1 || // Last page
                (i >= pageIndex - 1 && i <= pageIndex + 1); // Current page and adjacent pages

              // Show ellipsis for page gaps
              const showEllipsisBefore = i === pageIndex - 2 && pageIndex > 2;
              const showEllipsisAfter = i === pageIndex + 2 && pageIndex < totalPages - 3;

              if (showPageButton) {
                return (
                  <button
                    key={i}
                    onClick={() => table.setPageIndex(i)}
                    className={`w-10 h-10 mx-0.5 rounded-md ${
                      i === pageIndex
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50'
                    } font-medium text-sm transition-colors duration-150`}
                  >
                    {i + 1}
                  </button>
                );
              } else if (showEllipsisBefore || showEllipsisAfter) {
                return <span key={i} className="px-1 text-gray-400">...</span>;
              }

              return null;
            }).filter(Boolean)}
          </div>

          <button
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="p-2 rounded-md border border-gray-200 text-gray-600 bg-white hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed"
            title="Next Page"
          >
            {'>'}
          </button>
          <button
            onClick={() => table.setPageIndex(totalPages - 1)}
            disabled={!table.getCanNextPage()}
            className="p-2 rounded-md border border-gray-200 text-gray-600 bg-white hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed"
            title="Last Page"
          >
            {'>>'}
          </button>
        </div>

        {/* Page size selector */}
        <div className="flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-md border border-gray-200">
          <span className="text-sm text-gray-600">Show</span>
          <select
            value={pageSize}
            onChange={e => {
              setPageSize(Number(e.target.value))
              setPageIndex(0)
              setPageNo(0)
            }}
            className="border border-gray-300 rounded-md px-2 py-1 text-gray-700 bg-white focus:ring-2 focus:ring-blue-400 focus:outline-none"
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
          <span className="text-sm text-gray-600">per page</span>
        </div>
      </div>
    </>
  )
}
