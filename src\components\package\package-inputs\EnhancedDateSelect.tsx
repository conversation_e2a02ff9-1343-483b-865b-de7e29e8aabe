/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
// Removed unused import

interface DatePeriod {
  startDate: string;
  endDate: string;
}

interface EnhancedDateSelectProps {
  period: DatePeriod[];
  setDates: (startDate: string, endDate: string) => void;
  deleteDates: (startDate: string, endDate: string) => void;
}

export default function EnhancedDateSelect({ period, setDates, deleteDates }: EnhancedDateSelectProps) {
  const [selectedDates, setSelectedDates] = useState<DatePeriod[]>([]);
  // Removed unused state variables
  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [hoverDate, setHoverDate] = useState<string | null>(null);
  const [selectionMode, setSelectionMode] = useState<'start' | 'end'>('start');
  const [tempStartDate, setTempStartDate] = useState<string | null>(null);

  // Initialize selected dates from props
  useEffect(() => {
    setSelectedDates(period || []);
  }, [period]);

  // Format date as YYYY-MM-DD
  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  // Parse YYYY-MM-DD to Date
  const parseDate = (dateString: string): Date => {
    return new Date(dateString);
  };

  // Get days in month
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get day of week (0-6) for first day of month
  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };

  // Check if a date is in the past
  const isPastDate = (dateString: string): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const date = parseDate(dateString);
    return date < today;
  };

  // Check if a date is selected
  const isDateSelected = (dateString: string): boolean => {
    return selectedDates.some(period => {
      const start = parseDate(period.startDate);
      const end = parseDate(period.endDate);
      const date = parseDate(dateString);
      return date >= start && date <= end;
    });
  };

  // Check if a date is in the current selection range
  const isInSelectionRange = (dateString: string): boolean => {
    if (!tempStartDate || !hoverDate) return false;

    const date = parseDate(dateString);
    const start = parseDate(tempStartDate);
    const end = parseDate(hoverDate);

    return (date >= start && date <= end) || (date >= end && date <= start);
  };

  // Handle date click
  const handleDateClick = (dateString: string) => {
    if (isPastDate(dateString)) return;

    if (selectionMode === 'start') {
      setTempStartDate(dateString);
      setSelectionMode('end');
    } else {
      // Ensure start date is before end date
      const start = tempStartDate!;
      const end = dateString;

      if (parseDate(start) > parseDate(end)) {
        setDates(end, start);
      } else {
        setDates(start, end);
      }

      setTempStartDate(null);
      setSelectionMode('start');
    }
  };

  // Handle date hover
  const handleDateHover = (dateString: string) => {
    setHoverDate(dateString);
  };

  // Handle month navigation
  const handlePrevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
  };

  // Handle month/year selection
  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newMonth = parseInt(e.target.value);
    setCurrentMonth(new Date(currentMonth.getFullYear(), newMonth));
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newYear = parseInt(e.target.value);
    setCurrentMonth(new Date(newYear, currentMonth.getMonth()));
  };

  // Quick date range selection
  const handleQuickDateRange = (months: number) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const endDate = new Date(today);
    endDate.setMonth(today.getMonth() + months);

    setDates(formatDate(today), formatDate(endDate));
  };

  // Season-based date selection
  const handleSeasonSelection = (season: 'peak' | 'off' | 'regular' | 'financial-year-end') => {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;

    let startDate: Date, endDate: Date;

    switch(season) {
      case 'peak':
        // Example: December to January (winter peak)
        startDate = new Date(currentYear, 11, 15); // Dec 15
        endDate = new Date(nextYear, 1, 15); // Feb 15 next year
        break;
      case 'off':
        // Example: July to September (monsoon off-season)
        startDate = new Date(currentYear, 6, 1); // July 1
        endDate = new Date(currentYear, 8, 30); // Sept 30
        break;
      case 'regular':
        // Example: March to June (spring/summer regular season)
        startDate = new Date(currentYear, 2, 1); // March 1
        endDate = new Date(currentYear, 5, 30); // June 30
        break;
      case 'financial-year-end':
        // Financial year end (March 31st of next year)
        startDate = new Date(); // Today
        endDate = new Date(nextYear, 2, 31); // March 31st of next year
        break;
      default:
        startDate = new Date();
        endDate = new Date(nextYear, 2, 31); // March 31st of next year
        break;
    }

    setDates(formatDate(startDate), formatDate(endDate));
  };

  // Removed unused function

  // Handle delete period
  const handleDeletePeriod = (startDate: string, endDate: string) => {
    deleteDates(startDate, endDate);
  };

  // Handle clear all periods
  const handleClearAllPeriods = () => {
    selectedDates.forEach(period => {
      deleteDates(period.startDate, period.endDate);
    });
  };

  // Render calendar
  const renderCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(<div key={`empty-${i}`} className="h-8 w-8"></div>);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateString = formatDate(new Date(year, month, day));
      const isSelected = isDateSelected(dateString);
      const isPast = isPastDate(dateString);
      const isInRange = isInSelectionRange(dateString);
      const isTemp = tempStartDate === dateString;

      days.push(
        <div
          key={dateString}
          className={`
            h-8 w-8 flex items-center justify-center rounded-full cursor-pointer text-sm
            ${isPast ? 'text-gray-300 cursor-not-allowed' : 'hover:bg-blue-100'}
            ${isSelected ? 'bg-blue-500 text-white' : ''}
            ${isInRange && !isSelected ? 'bg-blue-100' : ''}
            ${isTemp ? 'bg-blue-600 text-white' : ''}
          `}
          onClick={() => !isPast && handleDateClick(dateString)}
          onMouseEnter={() => handleDateHover(dateString)}
        >
          {day}
        </div>
      );
    }

    // Generate month options
    const monthOptions = [];
    for (let i = 0; i < 12; i++) {
      const monthName = new Date(2000, i, 1).toLocaleString('default', { month: 'long' });
      monthOptions.push(
        <option key={i} value={i}>{monthName}</option>
      );
    }

    // Generate year options (current year + 5 years)
    const currentYear = new Date().getFullYear();
    const yearOptions = [];
    for (let i = currentYear; i <= currentYear + 5; i++) {
      yearOptions.push(
        <option key={i} value={i}>{i}</option>
      );
    }

    return (
      <div className="bg-white rounded-lg shadow-lg p-4 w-72">
        {/* Month/Year Selection */}
        <div className="flex justify-between items-center mb-4">
          <button
            className="text-gray-600 hover:text-gray-800 px-2 py-1"
            onClick={handlePrevMonth}
          >
            &lt;
          </button>

          <div className="flex space-x-2">
            <select
              value={month}
              onChange={handleMonthChange}
              className="text-sm border border-gray-300 rounded p-1"
            >
              {monthOptions}
            </select>

            <select
              value={year}
              onChange={handleYearChange}
              className="text-sm border border-gray-300 rounded p-1"
            >
              {yearOptions}
            </select>
          </div>

          <button
            className="text-gray-600 hover:text-gray-800 px-2 py-1"
            onClick={handleNextMonth}
          >
            &gt;
          </button>
        </div>

        {/* Quick Date Range Selection */}
        <div className="mb-4 flex flex-wrap gap-1">
          <button
            className="text-xs bg-blue-50 text-blue-700 hover:bg-blue-100 px-2 py-1 rounded"
            onClick={() => handleQuickDateRange(1)}
          >
            1 Month
          </button>
          <button
            className="text-xs bg-blue-50 text-blue-700 hover:bg-blue-100 px-2 py-1 rounded"
            onClick={() => handleQuickDateRange(3)}
          >
            3 Months
          </button>
          <button
            className="text-xs bg-blue-50 text-blue-700 hover:bg-blue-100 px-2 py-1 rounded"
            onClick={() => handleQuickDateRange(6)}
          >
            6 Months
          </button>
          <button
            className="text-xs bg-blue-50 text-blue-700 hover:bg-blue-100 px-2 py-1 rounded"
            onClick={() => handleQuickDateRange(12)}
          >
            1 Year
          </button>
        </div>

        {/* Season-based Selection */}
        <div className="mb-4 flex flex-wrap gap-1">
          <button
            className="text-xs bg-red-50 text-red-700 hover:bg-red-100 px-2 py-1 rounded"
            onClick={() => handleSeasonSelection('peak')}
          >
            Peak Season
          </button>
          <button
            className="text-xs bg-green-50 text-green-700 hover:bg-green-100 px-2 py-1 rounded"
            onClick={() => handleSeasonSelection('regular')}
          >
            Regular Season
          </button>
          <button
            className="text-xs bg-yellow-50 text-yellow-700 hover:bg-yellow-100 px-2 py-1 rounded"
            onClick={() => handleSeasonSelection('off')}
          >
            Off Season
          </button>
          <button
            className="text-xs bg-purple-50 text-purple-700 hover:bg-purple-100 px-2 py-1 rounded"
            onClick={() => handleSeasonSelection('financial-year-end')}
          >
            Until March 31st
          </button>
        </div>

        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
            <div key={day} className="h-8 w-8 flex items-center justify-center text-xs font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {days}
        </div>

        <div className="mt-4 text-xs text-gray-500">
          {selectionMode === 'start' ? 'Select start date' : 'Select end date'}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mt-4 p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Package Dates <span className="text-red-500">*</span></h3>
      </div>

      {/* Calendar toggle button */}
      <div className="mb-4">
        <button
          className="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 py-2 px-4 rounded-md text-sm font-medium border border-blue-200 flex items-center justify-center"
          onClick={() => setShowCalendar(!showCalendar)}
        >
          <span className="mr-2">{showCalendar ? "Hide Calendar" : "Show Calendar"}</span>
          {showCalendar ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"/>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
              <path d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>
            </svg>
          )}
        </button>
      </div>

      {/* Calendar */}
      {showCalendar && (
        <div className="mb-4">
          {renderCalendar()}
        </div>
      )}

      {/* Selected date periods */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <h4 className="text-sm font-medium text-gray-700">Selected Date Periods ({selectedDates.length})</h4>
          {selectedDates.length > 0 && (
            <button
              onClick={handleClearAllPeriods}
              className="text-xs text-red-600 hover:text-red-800 flex items-center"
            >
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Clear All
            </button>
          )}
        </div>

        {selectedDates.length === 0 ? (
          <div className="text-sm text-gray-500 text-center py-4 border border-dashed border-gray-300 rounded-md">
            No date periods selected. Use the calendar or date inputs above to add periods.
          </div>
        ) : (
          <div className="space-y-2 max-h-[200px] overflow-y-auto p-1 border border-gray-200 rounded-md bg-gray-50">
            {selectedDates.map((period) => (
              <div
                key={`${period.startDate}-${period.endDate}`}
                className="flex items-center justify-between p-2 bg-white border border-gray-100 rounded-md shadow-sm"
              >
                <div>
                  <span className="text-sm font-medium">
                    {new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()}
                  </span>
                  <div className="text-xs text-gray-500">
                    {Math.round((parseDate(period.endDate).getTime() - parseDate(period.startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1} days
                  </div>
                </div>
                <button
                  className="text-red-500 hover:text-red-700 w-6 h-6 rounded-full flex items-center justify-center hover:bg-red-50"
                  onClick={() => handleDeletePeriod(period.startDate, period.endDate)}
                  title="Remove date period"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
