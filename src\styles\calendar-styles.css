/* Custom Calendar Styles */

/* Improve overall calendar container */
.custom-calendar {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Make cells larger and more visible */
.custom-calendar .rdrDay {
  height: 70px;
  margin: 0;
  padding: 0;
  border: 1px solid #e5e7eb;
  position: relative;
}

/* Fix date display in each cell */
.custom-calendar .rdrDayNumber {
  position: absolute;
  top: 4px;
  left: 4px;
  font-weight: 500;
}

/* Style the day cells to be more structured */
.custom-calendar .rdrDay .rdrDayNumber span {
  color: #374151;
}

/* Make today's date more visible */
.custom-calendar .rdrDayToday .rdrDayNumber span:after {
  background: #3b82f6;
}

/* Improve month selector dropdown appearance */
.custom-calendar .rdrMonthPicker select, 
.custom-calendar .rdrYearPicker select {
  font-weight: 500;
  padding: 0.25rem 1rem 0.25rem 0.5rem;
  border-radius: 0.375rem;
  border-color: #e5e7eb;
  background-color: white;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.25rem center;
  background-size: 1rem;
}

.custom-calendar .rdrMonthPicker select:focus, 
.custom-calendar .rdrYearPicker select:focus {
  outline: none;
  border-color: #93c5fd;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Improve month navigation */
.custom-calendar .rdrMonthAndYearWrapper {
  padding: 0.75rem;
  height: auto;
  align-items: center;
  background-color: #f9fafb;
  border-bottom: 1px solid #f3f4f6;
}

.custom-calendar .rdrMonthAndYearPickers {
  margin: 0 0.5rem;
  flex: 1 1 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-calendar .rdrMonthPicker {
  margin-right: 0.5rem;
  max-width: 9rem;
  width: auto;
}

.custom-calendar .rdrYearPicker {
  max-width: 7rem;
  width: auto;
}

.custom-calendar .rdrNextPrevButton {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  width: 2rem;
  height: 2rem;
  margin: 0;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,0.05);
}

.custom-calendar .rdrNextPrevButton:hover {
  background-color: #f9fafb;
}

.custom-calendar .rdrNextPrevButton i {
  border-width: 2px;
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Custom styling for holiday indicators */
.custom-calendar .holiday-dot {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  height: 0.5rem;
  width: 0.5rem;
  border-radius: 50%;
  background-color: #8b5cf6;
}

/* Make weekend days slightly different color */
.custom-calendar .rdrDayWeekend:not(.rdrDayStartOfWeek, .rdrDayEndOfWeek) {
  background-color: #f8fafc;
}

/* Better week day headers */
.custom-calendar .rdrWeekDay {
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.7rem;
  padding: 0.75rem 0;
  background-color: #f3f4f6;
}

/* Better hover state */
.custom-calendar .rdrDay:not(.rdrDayPassive):hover .rdrDayNumber span {
  background-color: #e5e7eb;
}

/* Better cell selection state */
.custom-calendar .rdrDayStartOfMonth .rdrDayInPreview, 
.custom-calendar .rdrDayStartOfMonth .rdrDayEndPreview, 
.custom-calendar .rdrDayStartOfWeek .rdrDayInPreview,
.custom-calendar .rdrDayStartOfWeek .rdrDayEndPreview {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.custom-calendar .rdrDayEndOfMonth .rdrDayInPreview, 
.custom-calendar .rdrDayEndOfMonth .rdrDayStartPreview, 
.custom-calendar .rdrDayEndOfWeek .rdrDayInPreview,
.custom-calendar .rdrDayEndOfWeek .rdrDayStartPreview {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

/* Ensure price display */
.custom-calendar .rdrDay > div {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Fix price position and make it more visible */
.custom-calendar .rdrDay > div > div[class*="mt-auto"] {
  position: absolute;
  bottom: 2px;
  left: 2px;
  right: 2px;
  margin: 0 !important;
  font-size: 0.8rem !important;
  font-weight: 700 !important;
  color: #047857 !important;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.9) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  padding: 2px 4px !important;
  z-index: 10;
}

/* Make conflict borders more visible */
.custom-calendar .rdrDay div[class*="border-amber-400"] {
  border-width: 2px !important;
  border-color: #f59e0b !important;
  box-shadow: inset 0 0 0 1px rgba(245, 158, 11, 0.3);
}

/* Holiday indicators in specific position */
.custom-calendar .rdrDay > div > div:first-child {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  padding: 0.25rem;
}

/* Holiday name container */
.custom-calendar .rdrDay > div > div.absolute {
  z-index: 5;
}

/* Fixed height for days of the week headers */
.custom-calendar .rdrMonthName, 
.custom-calendar .rdrWeekDay {
  font-weight: 600;
  color: #4b5563;
}

/* Improved styles for the calendar month container */
.custom-calendar .rdrMonth {
  width: 100%;
  padding: 0;
}

/* Improve the appearance of the calendar days container */
.custom-calendar .rdrDays {
  margin-top: 4px;
}

/* Adjust styling for days not in current month */
.custom-calendar .rdrDayPassive {
  opacity: 0.5;
}

/* Make sure holiday text doesn't overflow */
.custom-calendar .rdrDay > div > div > div.text-\[9px\] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  font-size: 8px !important;
}

/* Ensure number appears on every day */
.custom-calendar .rdrDay:not(.rdrDayPassive) .rdrDayNumber {
  opacity: 1 !important;
}

/* Make the calendar larger */
.custom-calendar.calendar-large {
  font-size: 1.1rem;
  min-width: 700px;
  max-width: 100%;
  margin: 0 auto;
}
.custom-calendar.calendar-large .rdrMonth {
  width: 100%;
  min-width: 700px;
  max-width: 100%;
}
.custom-calendar.calendar-large .rdrDay {
  height: 80px;
  font-size: 1.05rem;
}

/* Holiday label tooltip effect */
.holiday-tooltip-trigger {
  position: relative;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.holiday-tooltip-trigger:hover, .holiday-tooltip-trigger:focus {
  background: #a78bfa;
  color: #fff;
  box-shadow: 0 2px 8px 0 rgba(168,139,250,0.15);
  z-index: 20;
}

/* Make the holiday label pop visually */
.holiday-tooltip-trigger {
  border: 1px solid #c4b5fd;
  font-weight: 700;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px 0 rgba(168,139,250,0.08);
} 