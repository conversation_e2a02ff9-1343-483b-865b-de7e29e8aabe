name: admin

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [main]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: admin using ssh
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd Tripmilestone-admin-fronend
            
            # Handle any uncommitted changes
            git stash
            
            # Ensure we're on the main branch
            git checkout main
            
            # Pull latest changes
            git pull origin main
            
            # Check git status
            git status
            
            # Set up Node environment
            export NVM_DIR=~/.nvm
            source ~/.nvm/nvm.sh      
            
            # Set production environment variables
            export VITE_API_SERVER_URL="https://api.tripxplo.com/v1/api/"
            export VITE_API_URL="https://api.tripxplo.com/v1"
            export VITE_FRONTEND_URL="https://admin.tripxplo.com"
            export VITE_TARIFF_API_URL="https://api.tripxplo.com/v1"
            export VITE_LINODE_STORAGE_URL="https://tripemilestone.in-maa-1.linodeobjects.com"
            
            # Clear npm cache to ensure fresh build
            npm cache clean --force
            
            # Install dependencies and build
            npm install
            npm run build
            
            # Restart PM2 - use app name or restart all
            pm2 restart admin-frontend || pm2 restart all
            
            # Show PM2 status
            pm2 list