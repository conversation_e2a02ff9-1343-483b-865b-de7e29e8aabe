import api from './auth';
import { SERVER_URL } from '../urls/urls';

// Get meal plans by room ID - general function
export const getMealPlansByRoomId = async (roomId: string) => {
  try {
    if (!roomId) {
      console.error("Invalid roomId provided (empty or undefined)");
      return [];
    }
    // Use the correct endpoint for fetching meal plans
    const URL = `admin/hotel/hotelRoom/${roomId}/mealPlan`;
    const response = await api.get(URL);

    if (response.data && response.data.success) {
      return response.data.data || [];
    } else {
      console.error("Error fetching meal plans:", response.data?.message);
      return [];
    }
  } catch (error) {
    console.error("Error fetching meal plans:", error);
    return [];
  }
};

// Special function for package meal plans that handles all possible URL issues
export const getPackageMealPlansByRoomId = async (roomId: string, hotelId: string = '') => {
  try {
    if (!roomId) {
      console.error("Invalid roomId provided (empty or undefined)");
      return [];
    }
    
    // Make a direct API call to the correct endpoint - constructing URL based on backend expectations
    // Using the pattern: /hotel/{hotelId}/hotelRoom/get/{roomId}/meal-plans
    let fullURL;
    
    // If hotelId is provided, use the full path pattern
    if (hotelId) {
      fullURL = `${SERVER_URL}admin/hotel/${hotelId}/hotelRoom/get/${roomId}/meal-plans`;
      console.log("Using hotelId + roomId URL:", fullURL);
    } else {
      // Try a different endpoint pattern as fallback
      fullURL = `${SERVER_URL}admin/hotel/hotelRoom/get/${roomId}/meal-plans`;
      console.log("Using fallback URL pattern:", fullURL);
    }
    
    console.log("Package Meal Plans full URL:", fullURL);
    
    // Using fetch instead of axios in case there's any axios interceptor/config issue
    const response = await fetch(fullURL, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      // If first attempt fails and we didn't use hotelId, try a completely different pattern
      if (!hotelId && response.status === 404) {
        console.log("First attempt failed. Trying alternative endpoint format...");
        const alternativeURL = `${SERVER_URL}admin/hotel/hotelRoom/${roomId}/meal-plans`;
        console.log("Alternative URL:", alternativeURL);
        
        const alternativeResponse = await fetch(alternativeURL, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!alternativeResponse.ok) {
          throw new Error(`All endpoint attempts failed. Last status: ${alternativeResponse.status}`);
        }
        
        const altData = await alternativeResponse.json();
        if (altData && altData.success) {
          return altData.data || [];
        }
      }
      
      throw new Error(`Failed to fetch meal plans: ${response.status}`);
    }
    
    const data = await response.json();
    
    if (data && data.success) {
      return data.data || [];
    } else {
      console.error("Error in meal plans response:", data?.message);
      return [];
    }
  } catch (error) {
    console.error("Package meal plans fetch error:", error);
    console.error("Room ID that failed:", roomId);
    return [];
  }
}; 