import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 3003,
    strictPort: false, // Allow fallback to next available port
    allowedHosts: ['admin.tripxplo.com']
  },
  build: {
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: {
          'radix-ui': [
            '@radix-ui/react-progress', 
            '@radix-ui/react-tabs',
            '@radix-ui/react-accordion',
            '@radix-ui/react-alert-dialog',
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-label',
            '@radix-ui/react-popover',
            '@radix-ui/react-switch'
          ]
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
  define: {
    // Define production environment variables
    'import.meta.env.VITE_API_SERVER_URL': JSON.stringify(process.env.VITE_API_SERVER_URL || 'https://api.tripxplo.com/v1/api/'),
    'import.meta.env.VITE_API_URL': JSON.stringify(process.env.VITE_API_URL || 'https://api.tripxplo.com/v1'),
    'import.meta.env.VITE_FRONTEND_URL': JSON.stringify(process.env.VITE_FRONTEND_URL || 'https://admin.tripxplo.com'),

  },
  optimizeDeps: {
    include: [
      '@radix-ui/react-progress', 
      '@radix-ui/react-tabs',
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-label',
      '@radix-ui/react-popover',
      '@radix-ui/react-switch'
    ]
  }
})
