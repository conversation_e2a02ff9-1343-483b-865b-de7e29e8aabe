{"name": "tripmilestone-admin-backend", "version": "1.0.0", "description": "Backend for Tripmilestone Admin Frontend", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js"}, "dependencies": {"axios": "^1.6.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "dotenv": "^16.3.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}}